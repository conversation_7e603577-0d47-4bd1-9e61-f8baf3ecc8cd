// Removed react-toastify imports to avoid conflicts with shadcn/ui toast system
// We will use a relative path for the API, so APIURL from api.ts is not strictly needed here for getReq
// import { APIURL } from "./api";

// API health check function
export const checkApiHealth = async (): Promise<boolean> => {
    try {
        const response = await fetch('/api/profile/', {
            method: 'HEAD',
            headers: { 'Accept': 'application/json' }
        });
        return response.ok;
    } catch {
        return false;
    }
};


// Generic HTTP request function
const makeRequest = async (url: string, method: string, data?: unknown) => {
    const fullUrl = `/api/${url}`;

    // Get auth token if available, but only for protected endpoints
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {
        "Content-Type": "application/json",
        "Accept": "application/json",
    };

    // Only add auth header for protected endpoints (not for login/register)
    const isAuthEndpoint = url.includes('auth/login') || url.includes('auth/register');
    if (token && !isAuthEndpoint) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const requestOptions: RequestInit = {
            method,
            headers,
        };

        // Handle method override for Django
        if (data && typeof data === 'object' && data !== null && '_method' in data) {
            const { _method, ...restData } = data as any;
            requestOptions.method = _method;
            if (Object.keys(restData).length > 0) {
                requestOptions.body = JSON.stringify(restData);
            }
        } else if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            requestOptions.body = JSON.stringify(data);
        }

        const response = await fetch(fullUrl, requestOptions);

        if (method === 'DELETE' && response.ok) {
            return { status: String(response.status), message: 'Deleted successfully' };
        }

        const responseData = await response.json();

        if (response.ok) {
            return { data: responseData, status: String(response.status) };
        }

        const errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;

        // Log error details for debugging in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`API Error: ${response.status} ${response.statusText} for ${fullUrl}`, responseData);
        }

        return { status: String(response.status || "400"), message: errorMsg };
    } catch (error: any) {
        // Log error details for debugging in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`Network Error for ${fullUrl}:`, error);
        }

        // Provide user-friendly error messages
        let userMessage = 'An error occurred';
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            userMessage = 'Unable to connect to server. Please check your internet connection.';
        } else if (error.message) {
            userMessage = error.message;
        }

        return { status: "400", message: userMessage };
    }
};

// JSON POST request
export const postReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'POST', data);
};

// PUT request
export const putReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'PUT', data);
};

// PATCH request
export const patchReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'PATCH', data);
};

// DELETE request
export const deleteReq = async (url: string) => {
    return makeRequest(url, 'DELETE');
};

// GET request
export const getReq = async (url: string) => {
    const fullUrl = `/api/${url}`; // Use relative path for Vite proxy

    // Get auth token if available
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {
        'Accept': 'application/json',
    };

    // Define which endpoints require authentication
    const protectedEndpoints = [
        'customer/',
        'admin/',
        'auth/user/',
        'auth/logout/'
    ];

    // Only add auth header for protected endpoints
    const requiresAuth = protectedEndpoints.some(endpoint => url.startsWith(endpoint));
    if (token && requiresAuth) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const response = await fetch(fullUrl, {
            headers,
        });

        if (!response.ok) {
            // Log error details for debugging in development
            if (process.env.NODE_ENV === 'development') {
                console.error(`API Error: ${response.status} ${response.statusText} for ${fullUrl}`);
            }
            return null;
        }

        const data = await response.json();
        // If the response is a paginated object with a `results` array, unwrap it for convenience
        const unwrapped = (data && typeof data === 'object' && Array.isArray(data.results)) ? data.results : data;
        return unwrapped;
    } catch (error: any) {
        // Log error details for debugging in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`Network Error for ${fullUrl}:`, error);
        }
        return null;
    }
};

// Generic multipart request function
const makeMultipartRequest = async (url: string, method: string, formData: FormData) => {
    const fullUrl = `/api/${url}`;

    // Get auth token if available
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {};

    // Define which endpoints require authentication
    const protectedEndpoints = [
        'customer/',
        'admin/',
        'auth/user/',
        'auth/logout/'
    ];

    // Only add auth header for protected endpoints
    const requiresAuth = protectedEndpoints.some(endpoint => url.startsWith(endpoint));
    if (token && requiresAuth) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const response = await fetch(fullUrl, {
            method,
            headers,
            body: formData
        });

        const responseData = await response.json();

        if (response.ok) {
            return { data: responseData, status: String(response.status) };
        }
        const errorMsg = responseData.message || responseData.detail || `HTTP error ${response.status}`;

        // Log error details for debugging in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`API Error: ${response.status} ${response.statusText} for ${fullUrl}`, responseData);
        }

        return { status: String(response.status || "400"), message: errorMsg };

    } catch (error: any) {
        // Log error details for debugging in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`Network Error for ${fullUrl}:`, error);
        }

        // Provide user-friendly error messages
        let userMessage = 'An error occurred';
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            userMessage = 'Unable to connect to server. Please check your internet connection.';
        } else if (error.message) {
            userMessage = error.message;
        }

        return { status: "400", message: userMessage };
    }
};

// Multipart/form-data POST request
export const postReqMultipart = async (url: string, formData: FormData) => {
    return makeMultipartRequest(url, 'POST', formData);
};

// Multipart/form-data PUT request
export const putReqMultipart = async (url: string, formData: FormData) => {
    return makeMultipartRequest(url, 'PUT', formData);
};