import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/contexts/ThemeContext';

const codeSnippets = [
  'const portfolio = () => {',
  'function createAmazingUI() {',
  'return <Portfolio />',
  'useEffect(() => {',
  'const [state, setState] = useState',
  'npm install awesome-packages',
  'git commit -m "Perfect code"',
  'export default App;',
  'import React from "react";',
  'const handleSubmit = async () => {',
  'console.log("Hello World!");',
  'interface Props {',
  'type Theme = "light" | "dark"',
  'const api = await fetch("/api")',
  'return response.json();',
];

const AnimatedBackground: React.FC = React.memo(() => {
  const { theme } = useTheme();
  const [lines, setLines] = useState<Array<{ id: number; text: string; top: number; delay: number }>>([]);

  useEffect(() => {
    if (theme !== 'dark') return;

    const generateLine = () => ({
      id: Math.random(),
      text: codeSnippets[Math.floor(Math.random() * codeSnippets.length)],
      top: Math.random() * 100,
      delay: Math.random() * 5,
    });

    const initialLines = Array.from({ length: 8 }, () => generateLine());
    setLines(initialLines);

    const interval = setInterval(() => {
      setLines(prevLines => [
        ...prevLines.slice(-7),
        generateLine(),
      ]);
    }, 3000);

    return () => clearInterval(interval);
  }, [theme]);

  if (theme !== 'dark') return null;

  return (
    <div className="animated-bg">
      {lines.map((line) => (
        <motion.div
          key={line.id}
          className="code-line"
          style={{
            top: `${line.top}%`,
            animationDelay: `${line.delay}s`,
          }}
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: '100vw', opacity: [0, 1, 1, 0] }}
          transition={{
            duration: 20,
            ease: 'linear',
            delay: line.delay,
          }}
        >
          {line.text}
        </motion.div>
      ))}
    </div>
  );
});

AnimatedBackground.displayName = 'AnimatedBackground';

export default AnimatedBackground;