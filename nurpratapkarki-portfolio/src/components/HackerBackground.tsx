import React, { useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/contexts/ThemeContext';

interface MatrixChar {
  id: number;
  char: string;
  x: number;
  delay: number;
  speed: number;
}

interface TerminalCommand {
  id: number;
  text: string;
  x: number;
  y: number;
  delay: number;
}

const matrixChars = 'アカサタナハマヤラワガザダバパイキシチニヒミリギジヂビピウクスツヌフムユルグズヅブプエケセテネヘメレゲゼデベペオコソンモヨロゴゾドボポヲ01'.split('');
const terminalCommands = [
  'npm install --save awesome',
  'git push origin main',
  'docker build -t app .',
  'kubectl apply -f deploy.yml',
  'terraform apply',
  'const magic = () => {}',
  'sudo systemctl start nginx',
  'ssh <EMAIL>',
  'curl -X POST /api/deploy',
  'npx create-react-app',
];

const HackerBackground: React.FC = () => {
  const { theme } = useTheme();
  const [matrixLines, setMatrixLines] = useState<MatrixChar[]>([]);
  const [terminalLines, setTerminalLines] = useState<TerminalCommand[]>([]);

  const generateMatrixChar = useCallback(() => ({
    id: Math.random(),
    char: matrixChars[Math.floor(Math.random() * matrixChars.length)],
    x: Math.random() * 100,
    delay: Math.random() * 5,
    speed: 0.5 + Math.random() * 1.5,
  }), []);

  const generateTerminalCommand = useCallback(() => ({
    id: Math.random(),
    text: terminalCommands[Math.floor(Math.random() * terminalCommands.length)],
    x: Math.random() * 80 + 10,
    y: Math.random() * 80 + 10,
    delay: Math.random() * 3,
  }), []);

  useEffect(() => {
    if (theme !== 'dark') return;

    // Matrix effect
    const matrixInterval = setInterval(() => {
      setMatrixLines(prev => [
        ...prev.slice(-12),
        generateMatrixChar(),
      ]);
    }, 200);

    // Terminal commands
    const terminalInterval = setInterval(() => {
      setTerminalLines(prev => [
        ...prev.slice(-3),
        generateTerminalCommand(),
      ]);
    }, 4000);

    // Initial generation
    setMatrixLines(Array.from({ length: 15 }, generateMatrixChar));
    
    return () => {
      clearInterval(matrixInterval);
      clearInterval(terminalInterval);
    };
  }, [theme, generateMatrixChar, generateTerminalCommand]);

  if (theme !== 'dark') return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {/* Matrix Rain */}
      {matrixLines.map((char) => (
        <motion.div
          key={char.id}
          className="absolute text-green-400 font-mono text-sm opacity-20"
          style={{
            left: `${char.x}%`,
            animationDelay: `${char.delay}s`,
          }}
          initial={{ y: -50, opacity: 0 }}
          animate={{ 
            y: window.innerHeight + 50, 
            opacity: [0, 0.3, 0.3, 0] 
          }}
          transition={{
            duration: 8 / char.speed,
            ease: 'linear',
            delay: char.delay,
          }}
        >
          {char.char}
        </motion.div>
      ))}

      {/* Terminal Commands */}
      {terminalLines.map((cmd) => (
        <motion.div
          key={cmd.id}
          className="absolute text-cyan-400 font-mono text-xs opacity-10"
          style={{
            left: `${cmd.x}%`,
            top: `${cmd.y}%`,
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: [0, 0.1, 0.1, 0], scale: 1 }}
          transition={{
            duration: 6,
            delay: cmd.delay,
          }}
        >
          <span className="text-green-400">$</span> {cmd.text}
        </motion.div>
      ))}

      {/* Circuit Board Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="circuit" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <path d="M0 50h20M80 50h20M50 0v20M50 80v20" stroke="#00ffff" strokeWidth="1"/>
              <circle cx="20" cy="50" r="3" fill="#00ffff"/>
              <circle cx="80" cy="50" r="3" fill="#00ffff"/>
              <circle cx="50" cy="20" r="3" fill="#00ffff"/>
              <circle cx="50" cy="80" r="3" fill="#00ffff"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#circuit)"/>
        </svg>
      </div>

      {/* Glitch Lines */}
      <motion.div
        className="absolute inset-0 opacity-5"
        animate={{
          background: [
            'linear-gradient(90deg, transparent 0%, #00ffff 50%, transparent 100%)',
            'linear-gradient(90deg, transparent 20%, #00ff00 60%, transparent 80%)',
            'linear-gradient(90deg, transparent 40%, #00ffff 80%, transparent 100%)',
          ]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
    </div>
  );
};

export default HackerBackground;