import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface Skill {
  name: string;
  level: number;
  category: string;
}

interface Experience {
  title: string;
  company: string;
  period: string;
  description: string;
  technologies: string[];
}

const skills: Skill[] = [
  { name: 'React/Next.js', level: 95, category: 'Frontend' },
  { name: 'TypeScript', level: 90, category: 'Language' },
  { name: 'Node.js', level: 88, category: 'Backend' },
  { name: 'Python', level: 85, category: 'Language' },
  { name: 'PostgreSQL', level: 82, category: 'Database' },
  { name: 'AWS/Cloud', level: 80, category: 'DevOps' },
  { name: 'Docker', level: 78, category: 'DevOps' },
  { name: 'GraphQL', level: 75, category: 'API' },
];

const experiences: Experience[] = [
  {
    title: 'Senior Full Stack Developer',
    company: 'TechCorp Solutions',
    period: '2022 - Present',
    description: 'Lead development of enterprise web applications serving 100k+ users. Architected microservices infrastructure and mentored junior developers.',
    technologies: ['React', 'Node.js', 'AWS', 'PostgreSQL', 'Docker'],
  },
  {
    title: 'Full Stack Developer',
    company: 'StartupX',
    period: '2020 - 2022',
    description: 'Built and scaled web applications from MVP to production. Implemented CI/CD pipelines and optimized application performance.',
    technologies: ['Vue.js', 'Python', 'MongoDB', 'GCP', 'Jenkins'],
  },
  {
    title: 'Frontend Developer',
    company: 'Digital Agency',
    period: '2019 - 2020',
    description: 'Developed responsive web applications and collaborated with design teams to create pixel-perfect user interfaces.',
    technologies: ['React', 'TypeScript', 'Sass', 'Webpack', 'Figma'],
  },
];

const About: React.FC = React.memo(() => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring' as const,
        stiffness: 100,
        damping: 12,
      },
    },
  };

  const handleDownloadResume = () => {
    // In a real application, this would download the actual resume file
    const link = document.createElement('a');
    link.href = '#';
    link.download = 'Nur_Pratap_Karki_Resume.pdf';
    link.click();
  };

  return (
    <section id="about" className="py-24">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">
            About Me
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Passionate full-stack developer with 5+ years of experience creating scalable web applications
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Personal Info */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-2xl">Who I Am</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  I'm a passionate full-stack developer who loves creating beautiful, functional web applications. 
                  With over 5 years of experience, I've worked with startups and enterprises to build scalable solutions 
                  that make a real impact.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, 
                  or sharing knowledge with the developer community through blog posts and mentoring.
                </p>
                <div className="pt-4">
                  <Button 
                    onClick={handleDownloadResume}
                    variant="hero"
                  >
                    Download Resume
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Skills */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-2xl">Technical Skills</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{skill.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {skill.category}
                      </Badge>
                    </div>
                    <Progress 
                      value={skill.level} 
                      className="h-2 bg-muted"
                    />
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Experience Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Experience</h3>
            <p className="text-muted-foreground">My professional journey</p>
          </div>

          <motion.div
            className="space-y-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {experiences.map((experience, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative"
              >
                <Card className="glass-card ml-8 relative">
                  {/* Timeline connector */}
                  <div className="absolute -left-8 top-6 w-4 h-4 bg-gradient-primary rounded-full border-4 border-background" />
                  {index < experiences.length - 1 && (
                    <div className="absolute -left-6 top-10 w-0.5 h-full bg-border" />
                  )}

                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                      <div>
                        <CardTitle className="text-xl">{experience.title}</CardTitle>
                        <p className="text-primary font-medium">{experience.company}</p>
                      </div>
                      <Badge variant="outline" className="text-sm w-fit">
                        {experience.period}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4 leading-relaxed">
                      {experience.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {experience.technologies.map((tech) => (
                        <Badge key={tech} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
});

About.displayName = 'About';

export default About;