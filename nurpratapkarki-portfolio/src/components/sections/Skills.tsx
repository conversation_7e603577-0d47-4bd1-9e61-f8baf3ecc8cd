import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { getReq } from '@/api/apiService';

interface Skill {
  id: number;
  name: string;
  proficiency: number;
  years_experience: number;
}

interface SkillCategory {
  id: number;
  name: string;
  color: string;
  technologies: string[];
  skills: Skill[];
  order: number;
}

interface ProfileData {
  id: number;
  name: string;
  title: string;
  years_experience: number;
  projects_completed: number;
  technologies_mastered: number;
  client_satisfaction: number;
  available_for_work: boolean;
}

const Skills: React.FC = () => {
  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>([]);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch skills and profile data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch skill categories
        const categoriesData = await getReq('skills/categories/');
        if (categoriesData) {
          setSkillCategories(categoriesData.results || categoriesData);
        }

        // Fetch profile data for achievements
        const profileData = await getReq('profile/current/');
        if (profileData) {
          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching skills data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Format achievements from profile data
  const achievements = profile ? [
    { number: `${profile.projects_completed}+`, label: 'Projects Completed' },
    { number: `${profile.years_experience}+`, label: 'Years Experience' },
    { number: `${profile.technologies_mastered}+`, label: 'Technologies Mastered' },
    { number: `${profile.client_satisfaction}%`, label: 'Client Satisfaction' }
  ] : [];
  return (
    <section className="py-20 px-4 bg-muted/30">
      <div className="container mx-auto max-w-6xl">
        {/* Skills Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="gradient-text">Skills & Technologies</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            A comprehensive toolkit for building modern, scalable web applications
          </p>
        </motion.div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Loading skills data...</p>
          </div>
        )}

        {/* Achievement Counters */}
        {!loading && achievements.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
          >
            {achievements.map((achievement, index) => (
            <motion.div
              key={achievement.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <motion.div
                className="text-3xl md:text-4xl font-bold gradient-text mb-2"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                {achievement.number}
              </motion.div>
              <div className="text-muted-foreground text-sm md:text-base">
                {achievement.label}
              </div>
            </motion.div>
          ))}
          </motion.div>
        )}

        {/* Skills Grid */}
        {!loading && (
          <div className="grid md:grid-cols-2 gap-8">
            {skillCategories.map((skillGroup, index) => (
            <motion.div
              key={skillGroup.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="glass-card h-full">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className={`w-4 h-8 rounded-full bg-gradient-to-b ${skillGroup.color}`} />
                    <h3 className="text-xl font-semibold">{skillGroup.name}</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    {skillGroup.technologies.map((tech, techIndex) => (
                      <motion.div
                        key={tech}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.5 + techIndex * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ scale: 1.05 }}
                        className="flex items-center gap-2 p-2 rounded-lg bg-background/50 hover:bg-background/80 transition-colors cursor-default"
                      >
                        <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${skillGroup.color}`} />
                        <span className="text-sm font-medium">{tech}</span>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
          </div>
        )}

        {/* Status Indicator */}
        {!loading && profile && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full glass-card">
              <motion.div
                className={`w-3 h-3 rounded-full ${profile.available_for_work ? 'bg-green-500' : 'bg-red-500'}`}
                animate={{ scale: [1, 1.2, 1], opacity: [1, 0.7, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <span className="text-sm font-medium">
                {profile.available_for_work ? 'Available for new opportunities' : 'Currently unavailable'}
              </span>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default Skills;