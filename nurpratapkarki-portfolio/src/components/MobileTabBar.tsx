import React from 'react';
import { motion } from 'framer-motion';
import { Home, FolderOpen, User, Zap, Mail } from 'lucide-react';
import { useLocation } from 'react-router-dom';

interface TabItem {
  id: string;
  label: string;
  icon: React.ElementType;
  section?: string;
}

interface MobileTabBarProps {
  onSectionClick: (section: string) => void;
}

const tabs: TabItem[] = [
  { id: 'hero', label: 'Home', icon: Home },
  { id: 'projects', label: 'Projects', icon: FolderOpen },
  { id: 'about', label: 'About', icon: User },
  { id: 'skills', label: 'Skills', icon: Zap },
  { id: 'contact', label: 'Contact', icon: Mail },
];

const MobileTabBar: React.FC<MobileTabBarProps> = ({ onSectionClick }) => {
  const location = useLocation();
  const isHomePage = location.pathname === '/';
  
  // Get active section from URL hash or default to hero
  const getActiveSection = () => {
    if (!isHomePage) {
      return location.pathname.replace('/', '') || 'hero';
    }
    
    // Check if there's a hash in the URL
    const hash = window.location.hash.replace('#', '');
    return hash || 'hero';
  };
  
  const [activeSection, setActiveSection] = React.useState(getActiveSection());

  // Listen for hash changes and section updates
  React.useEffect(() => {
    const handleHashChange = () => {
      setActiveSection(getActiveSection());
    };

    const handleScroll = () => {
      if (!isHomePage) return;
      
      // Check which section is currently in view
      const sections = ['hero', 'projects', 'about', 'skills', 'contact'];
      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    window.addEventListener('scroll', handleScroll);
    
    // Set initial active section
    setActiveSection(getActiveSection());

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isHomePage]);

  const handleTabClick = (tab: TabItem) => {
    if (isHomePage) {
      onSectionClick(tab.id);
      setActiveSection(tab.id);
    } else {
      // Navigate to home and scroll to section
      window.location.href = `/#${tab.id}`;
    }
  };

  return (
    <motion.div
      className="fixed bottom-0 left-0 right-0 z-50 md:hidden bg-background/90 backdrop-blur-md border-t border-border"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex justify-around items-center px-4 py-2">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeSection === tab.id;

          return (
            <motion.button
              key={tab.id}
              onClick={() => handleTabClick(tab)}
              className={`flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-colors relative ${
                isActive
                  ? 'text-primary'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              whileTap={{ scale: 0.95 }}
              whileHover={{ scale: 1.05 }}
            >
              {isActive && (
                <motion.div
                  className="absolute inset-0 bg-primary/10 rounded-lg"
                  layoutId="activeTab"
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                />
              )}
              <Icon className="w-5 h-5 mb-1" />
              <span className="text-xs font-medium">{tab.label}</span>
            </motion.button>
          );
        })}
      </div>
    </motion.div>
  );
};

export default MobileTabBar;