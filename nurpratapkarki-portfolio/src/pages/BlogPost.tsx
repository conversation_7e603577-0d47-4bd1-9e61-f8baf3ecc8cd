import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ThemeProvider } from '@/contexts/ThemeContext';
import Navigation from '@/components/Navigation';
import HackerBackground from '@/components/HackerBackground';
import MouseTrail from '@/components/MouseTrail';
import MobileTabBar from '@/components/MobileTabBar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  Calendar,
  Clock,
  ArrowLeft,
  Share2,
  Link2
} from 'lucide-react';
import { toast } from 'sonner';
import { getReq } from '@/api/apiService';

interface BlogTag {
  id: number;
  name: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  image: string | null;
  category: string;
  tags: BlogTag[];
  read_time: string;
  featured: boolean;
  date: string;
  published_date: string;
}

// Static blog data removed - using API data instead



const BlogPost = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [readingProgress, setReadingProgress] = useState(0);
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch blog post and related posts from API
  useEffect(() => {
    const fetchData = async () => {
      if (!slug) return;

      try {
        setLoading(true);

        // Fetch the main blog post
        const postData = await getReq(`blog/posts/${slug}/`);
        if (postData) {
          setPost(postData);

          // Fetch related posts (same category, excluding current post)
          const relatedData = await getReq(`blog/posts/?category=${postData.category}`);
          if (relatedData) {
            const related = (relatedData.results || relatedData)
              .filter((p: BlogPost) => p.slug !== slug)
              .slice(0, 3);
            setRelatedPosts(related);
          }
        }
      } catch (error) {
        console.error('Error fetching blog data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  useEffect(() => {
    const handleScroll = () => {
      const article = document.getElementById('blog-content');
      if (article) {
        const scrollTop = window.scrollY;
        const docHeight = article.offsetHeight;
        const winHeight = window.innerHeight;
        const scrollPercent = scrollTop / (docHeight - winHeight);
        const progress = Math.min(100, Math.max(0, scrollPercent * 100));
        setReadingProgress(progress);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = post?.title || '';
    
    let shareUrl = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        toast.success('Link copied to clipboard!');
        return;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  const handleSectionClick = (section: string) => {
    if (section === 'hero') {
      navigate('/');
    } else if (section === 'blog') {
      navigate('/blog');
    }
  };

  if (loading) {
    return (
      <ThemeProvider>
        <div className="min-h-screen bg-background text-foreground">
          <HackerBackground />
          <MouseTrail />
          <Navigation activeSection="blog" onSectionClick={handleSectionClick} />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading blog post...</p>
            </div>
          </div>
          <MobileTabBar onSectionClick={handleSectionClick} />
        </div>
      </ThemeProvider>
    );
  }

  if (!post) {
    return (
      <ThemeProvider>
        <div className="min-h-screen bg-background text-foreground">
          <HackerBackground />
          <MouseTrail />
          <Navigation activeSection="blog" onSectionClick={handleSectionClick} />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-4">Post Not Found</h1>
              <p className="text-muted-foreground mb-6">The blog post you're looking for doesn't exist.</p>
              <Button onClick={() => navigate('/blog')}>
                Back to Blog
              </Button>
            </div>
          </div>
          <MobileTabBar onSectionClick={handleSectionClick} />
        </div>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-background text-foreground">
        <HackerBackground />
        <MouseTrail />
        
        {/* Reading Progress Bar */}
        <div className="fixed top-0 left-0 right-0 z-50">
          <Progress value={readingProgress} className="h-1 rounded-none" />
        </div>
        
        <Navigation 
          activeSection="blog" 
          onSectionClick={handleSectionClick} 
        />

        <main className="pt-20">
          {/* Article Header */}
          <section className="py-12 px-4">
            <div className="container mx-auto max-w-4xl">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Button
                  variant="ghost"
                  onClick={() => navigate('/blog')}
                  className="mb-8 -ml-4"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Blog
                </Button>

                <div className="mb-8">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <Badge variant="secondary">{post.category}</Badge>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {new Date(post.date).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {post.read_time}
                    </span>
                  </div>

                  <h1 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">
                    {post.title}
                  </h1>

                  <p className="text-xl text-muted-foreground mb-8">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center gap-2 mb-6">
                    {post.tags.map(tag => (
                      <Badge key={tag.id} variant="outline">
                        {tag.name}
                      </Badge>
                    ))}
                  </div>

                  {/* Share Buttons */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground mr-2">Share:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare('twitter')}
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare('linkedin')}
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare('copy')}
                    >
                      <Link2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>

          {/* Article Content */}
          <section className="pb-12 px-4">
            <div className="container mx-auto max-w-4xl">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                id="blog-content"
                className="prose prose-lg dark:prose-invert max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: post.content.split('\n').map(line => {
                    if (line.startsWith('# ')) {
                      return `<h1>${line.substring(2)}</h1>`;
                    } else if (line.startsWith('## ')) {
                      return `<h2>${line.substring(3)}</h2>`;
                    } else if (line.startsWith('### ')) {
                      return `<h3>${line.substring(4)}</h3>`;
                    } else if (line.startsWith('```')) {
                      return line.includes('```jsx') || line.includes('```javascript') || line.includes('```') 
                        ? '<pre class="bg-card p-4 rounded-lg overflow-x-auto"><code>'
                        : '</code></pre>';
                    } else if (line.trim() === '') {
                      return '<br/>';
                    } else {
                      return `<p>${line}</p>`;
                    }
                  }).join('')
                }}
              />
            </div>
          </section>

          {/* Related Posts */}
          <section className="py-12 px-4 border-t border-border">
            <div className="container mx-auto max-w-4xl">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h2 className="text-2xl font-bold mb-8 flex items-center gap-2">
                  <span className="w-2 h-8 bg-gradient-primary rounded-full"></span>
                  Related Articles
                </h2>
                
                <div className="grid md:grid-cols-3 gap-6">
                  {relatedPosts.slice(0, 3).map((relatedPost, index) => (
                    <motion.div
                      key={relatedPost.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                    >
                      <Link to={`/blog/${relatedPost.slug}`}>
                        <Card className="glass-card h-full hover:scale-105 transition-transform duration-300">
                          <CardHeader>
                            <CardTitle className="text-lg hover:text-primary transition-colors">
                              {relatedPost.title}
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex items-center justify-between">
                              <Badge variant="secondary">{relatedPost.category}</Badge>
                              <span className="text-sm text-muted-foreground flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {relatedPost.read_time}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </section>
        </main>

        <MobileTabBar onSectionClick={handleSectionClick} />
      </div>
    </ThemeProvider>
  );
};

export default BlogPost;