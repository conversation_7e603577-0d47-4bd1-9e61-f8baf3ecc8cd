import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Github, ExternalLink } from 'lucide-react';
import { type Project } from '@/components/sections/Projects';
import { getReq } from '@/api/apiService';

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        const projectData = await getReq(`projects/${id}/`);
        if (projectData) {
          setProject(projectData);
        }
      } catch (error) {
        console.error('Error fetching project details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-muted-foreground">Loading project details...</p>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="glass-card max-w-md mx-auto text-center">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-bold mb-4">Project Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The project you're looking for doesn't exist.
            </p>
            <Button onClick={() => navigate('/')}>
              Return to Portfolio
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleBackClick = () => {
    navigate('/', { replace: true });
    // Scroll to projects section after navigation
    setTimeout(() => {
      document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <motion.nav
        className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Button
            variant="outline"
            onClick={handleBackClick}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Portfolio
          </Button>
          
          <div className="text-xl font-bold gradient-text">
            Portfolio
          </div>
        </div>
      </motion.nav>

      <div className="pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Project Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h1
              className="text-4xl md:text-6xl font-bold mb-4 gradient-text"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: 'spring' }}
            >
              {project.title}
            </motion.h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {project.description}
            </p>
          </motion.div>

          {/* Project Image */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <img
                src={project.image || 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=500&fit=crop&crop=center'}
                alt={project.title}
                className="w-full h-96 md:h-[500px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              
              {project.featured && (
                <Badge className="absolute top-6 left-6 bg-gradient-primary text-white text-lg px-4 py-2">
                  Featured Project
                </Badge>
              )}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Project Details */}
            <motion.div
              className="lg:col-span-2 space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-2xl">Project Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-primary">The Challenge</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      This project was built to address the need for a modern, scalable solution in the {project.category_display.toLowerCase()} space.
                      The main challenges included creating an intuitive user experience while maintaining high performance and security standards.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-primary">The Solution</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      I developed a comprehensive solution using cutting-edge technologies and best practices. 
                      The architecture focuses on scalability, maintainability, and user experience, resulting in a robust application 
                      that meets all requirements and exceeds performance expectations.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-primary">Key Features</h3>
                    <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                      <li>Responsive design that works on all devices</li>
                      <li>High-performance architecture with optimized loading times</li>
                      <li>Modern UI/UX with intuitive navigation</li>
                      <li>Secure authentication and data handling</li>
                      <li>Real-time updates and notifications</li>
                      <li>Comprehensive testing and error handling</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-2xl">Development Process</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">Planning & Design</h4>
                      <p className="text-sm text-muted-foreground">
                        Started with user research and wireframing to ensure optimal user experience.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Development</h4>
                      <p className="text-sm text-muted-foreground">
                        Built using modern frameworks with a focus on clean, maintainable code.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Testing</h4>
                      <p className="text-sm text-muted-foreground">
                        Comprehensive testing including unit, integration, and end-to-end tests.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Deployment</h4>
                      <p className="text-sm text-muted-foreground">
                        Deployed using CI/CD pipelines with automated monitoring and scaling.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Project Info Sidebar */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Category</h4>
                    <Badge variant="secondary">{project.category_display}</Badge>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Technologies</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech) => (
                        <Badge key={tech} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Status</h4>
                    <Badge className="bg-green-500 text-white">Completed</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card">
                <CardHeader>
                  <CardTitle>Links</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => window.open(project.live_url, '_blank')}
                    variant="hero"
                    className="w-full"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Live Demo
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => window.open(project.github_url, '_blank')}
                    className="w-full"
                  >
                    <Github className="w-4 h-4 mr-2" />
                    View Source Code
                  </Button>
                </CardContent>
              </Card>

              <Card className="glass-card">
                <CardContent className="pt-6">
                  <h4 className="font-semibold mb-3">Like what you see?</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Let's discuss how I can help bring your next project to life.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigate('/');
                      setTimeout(() => {
                        document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                      }, 100);
                    }}
                    className="w-full"
                  >
                    Get In Touch
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetail;