@tailwind base;
@tailwind components;
@tailwind utilities;

/* Portfolio Design System - Full Stack Developer Theme */

@layer base {
  :root {
    /* Light Mode */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary Gradient Colors - Bluish Purple */
    --primary: 248 93% 65%;
    --primary-variant: 263 85% 65%;
    --primary-foreground: 210 40% 98%;

    /* Accent - Electric Blue */
    --accent: 194 100% 50%;
    --accent-foreground: 222.2 84% 4.9%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 248 93% 65%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-variant)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-variant)) 50%, hsl(var(--accent)) 100%);
    --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Shadows */
    --shadow-primary: 0 10px 30px -10px hsl(var(--primary) / 0.3);
    --shadow-accent: 0 0 40px hsl(var(--accent) / 0.2);
    --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.1);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Mode - Complete Black Hacker Theme */
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;

    --card: 0 0% 5%;
    --card-foreground: 210 40% 98%;

    --popover: 232 84% 12%;
    --popover-foreground: 210 40% 98%;

    /* Maintain Primary Colors in Dark Mode */
    --primary: 248 93% 65%;
    --primary-variant: 263 85% 65%;
    --primary-foreground: 222.2 84% 4.9%;

    /* Electric Blue Accent */
    --accent: 194 100% 50%;
    --accent-foreground: 210 40% 98%;

    --secondary: 232 32% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 232 32% 20%;
    --muted-foreground: 215 20.2% 65.1%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 232 32% 20%;
    --input: 232 32% 20%;
    --ring: 248 93% 65%;

    /* Dark Mode Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-variant)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(232 84% 15%) 50%, hsl(232 84% 20%) 100%);
    --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));

    /* Dark Mode Shadows */
    --shadow-primary: 0 10px 30px -10px hsl(var(--primary) / 0.5);
    --shadow-accent: 0 0 40px hsl(var(--accent) / 0.3);
    --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    transition: var(--transition-smooth);
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Animated Background for Dark Mode */
  .animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.05;
  }

  .code-line {
    position: absolute;
    white-space: nowrap;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: hsl(var(--accent));
    animation: float-code 20s linear infinite;
  }

  @keyframes float-code {
    0% { transform: translateX(-100px) translateY(0px); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(calc(100vw + 100px)) translateY(-20px); opacity: 0; }
  }

  /* Gradient Text */
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass Morphism Card */
  .glass-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }

  .glass-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-primary);
  }

  /* Hero Button Styles */
  .hero-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    transition: var(--transition-bounce);
    box-shadow: var(--shadow-primary);
  }

  .hero-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-accent);
  }

  /* Typing Animation */
  .typing-text::after {
    content: '|';
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
}