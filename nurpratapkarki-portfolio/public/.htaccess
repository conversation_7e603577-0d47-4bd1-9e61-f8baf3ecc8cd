# .htaccess for Nur Pratap Karki Portfolio
# Full Stack Web Developer Nepal - SEO Optimizations

# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION BEGIN
PassengerAppRoot "/home/<USER>/backend"
PassengerBaseURI "/backend"
PassengerPython "/home/<USER>/virtualenv/backend/3.13/bin/python"
# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION END

# Enable mod_rewrite
RewriteEngine On
RewriteBase /

# Skip rewrite for backend routes (MUST BE FIRST)
RewriteCond %{REQUEST_URI} ^/backend/
RewriteRule ^ - [L]

# Force HTTPS (SSL) - but skip for backend
RewriteCond %{HTTPS} off
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Force WWW or non-WWW (choose one)
# Force non-WWW (recommended for portfolio sites) - but skip for backend
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Remove trailing slashes (except for directories) - but skip for backend
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+[^?\s]*?/[\s?] [NC]
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule ^(.*)/ /$1 [R=301,L]

# Add trailing slash to directories - but skip for backend
RewriteCond %{REQUEST_FILENAME} -d
RewriteCond %{REQUEST_URI} !/$
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule . %{REQUEST_URI}/ [R=301,L]

# Frontend SPA routing rules (React Router)
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/backend/
RewriteRule . /index.html [L]

# Security Headers
<IfModule mod_headers.c>
    # X-Frame-Options
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # X-Content-Type-Options
    Header always set X-Content-Type-Options "nosniff"
    
    # X-XSS-Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/avif "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 day"
    
    # XML and JSON
    ExpiresByType application/xml "access plus 1 day"
    ExpiresByType application/json "access plus 1 day"
    
    # Favicon
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|avif|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>
</IfModule>

# MIME Types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType image/webp .webp
    AddType image/avif .avif
</IfModule>

# Error Pages (create custom 404 page for better UX)
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Block access to .htaccess itself
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Block access to package files
<FilesMatch "(package\.json|package-lock\.json|yarn\.lock|composer\.json|composer\.lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>
