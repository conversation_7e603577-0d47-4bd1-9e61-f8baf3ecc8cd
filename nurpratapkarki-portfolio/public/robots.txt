# Robots.txt for Nur Pratap Karki Portfolio
# Full Stack Web Developer Nepal

# Allow all search engines full access to crawl the entire website
User-agent: *
Allow: /

# Specific directives for major search engines - full access
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 1

User-agent: DuckDuckBot
Allow: /
Crawl-delay: 1

User-agent: Bai<PERSON><PERSON>er
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 1

# Social media crawlers - full access
User-agent: Twitterbot
Allow: /

User-agent: facebookexternalhit
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

User-agent: TelegramBot
Allow: /

# Only block system/development files that are not meant for public access
# These files are typically not served by web servers anyway
Disallow: /.git/
Disallow: /.env*
Disallow: /node_modules/

# Allow all public content including:
# - All pages and routes
# - All images and media files
# - All CSS and JavaScript files
# - All fonts and assets
# - All API endpoints (if any)
# - All documentation
# - All portfolio content

# Explicitly allow important files
Allow: /favicon.ico
Allow: /robots.txt
Allow: /sitemap.xml
Allow: /manifest.json
Allow: /*.css
Allow: /*.js
Allow: /*.json
Allow: /*.xml
Allow: /*.txt
Allow: /assets/
Allow: /images/
Allow: /docs/
Allow: /api/

# Sitemap location
Sitemap: https://nurpratapkarki.com.np/sitemap.xml

# Host directive (replace with your actual domain)
Host: https://nurpratapkarki.com.np
