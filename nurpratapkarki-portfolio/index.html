<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary SEO Meta Tags -->
    <title>Nur Pratap Karki - Full Stack Developer | React, Python Expert | Global & Nepal</title>
    <meta name="title" content="Nur Pratap Karki - Full Stack Developer | React,  Python Expert | Global & Nepal" />
    <meta name="description" content="Expert Full Stack Developer specializing in React,  Python, TypeScript, and modern web technologies. Building scalable web applications, APIs, and cloud solutions globally. Available for remote work worldwide and local projects in Nepal." />
    <meta name="author" content="Nur Pratap Karki" />
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
    <meta name="googlebot" content="index, follow" />

    <!-- Keywords for SEO - Global First, Then Local -->
    <meta name="keywords" content="Full Stack Developer, Web Developer, Frontend Developer, Backend Developer, React Developer, Node.js Developer, Python Developer, JavaScript Developer, TypeScript Developer, MERN Stack Developer, Full Stack Engineer, Software Engineer, Web Development Services, Remote Developer, Freelance Developer, Software Developer, API Developer, Database Developer, Cloud Developer, DevOps Engineer, React.js Expert, Node.js Expert, Python Expert, JavaScript Expert, TypeScript Expert, Modern Web Technologies, Responsive Web Design, Progressive Web Apps, E-commerce Development, Custom Web Solutions, SaaS Development, Mobile App Development, UI/UX Developer, Frontend Engineer, Backend Engineer, Full Stack Developer Nepal, Web Developer Nepal, React Developer Nepal, Node.js Developer Nepal, Python Developer Nepal, Software Developer Nepal, Web Developer Kathmandu, Freelance Developer Nepal, Remote Developer Nepal, Nur Pratap Karki" />

    <!-- Geographic and Location SEO - Global with Nepal Base -->
    <meta name="geo.region" content="NP" />
    <meta name="geo.country" content="Nepal" />
    <meta name="geo.placename" content="Kathmandu, Nepal" />
    <meta name="ICBM" content="27.7172, 85.3240" />
    <meta name="DC.title" content="Nur Pratap Karki - Full Stack Developer | Global Remote Work" />

    <!-- Language and Content -->
    <meta name="language" content="English" />
    <meta name="content-language" content="en" />
    <meta name="distribution" content="global" />
    <meta name="rating" content="general" />
    <meta name="revisit-after" content="3 days" />
    <meta name="coverage" content="worldwide" />
    <meta name="target" content="all" />
    <meta name="audience" content="all" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nurpratapkarki.com.np/" />
    <meta property="og:title" content="Nur Pratap Karki - Full Stack Developer | React, Node.js, Python Expert" />
    <meta property="og:description" content="Expert Full Stack Developer specializing in React, Node.js, Python, and modern web technologies. Building scalable web applications globally with remote work expertise." />
    <meta property="og:image" content="https://nurpratapkarki.com.np/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Nur Pratap Karki - Full Stack Web Developer Portfolio" />
    <meta property="og:site_name" content="Nur Pratap Karki - Full Stack Developer" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nurpratapkarki.com.np/" />
    <meta property="twitter:title" content="Nur Pratap Karki - Full Stack Developer | Global Remote Expert" />
    <meta property="twitter:description" content="Expert Full Stack Developer specializing in React, Node.js, Python. Available for remote work worldwide and local projects." />
    <meta property="twitter:image" content="https://nurpratapkarki.com.np/og-image.jpg" />
    <meta property="twitter:image:alt" content="Nur Pratap Karki - Full Stack Web Developer Portfolio" />
    <meta property="twitter:creator" content="@nurpratapkarki" />
    <meta property="twitter:site" content="@nurpratapkarki" />

    <!-- LinkedIn -->
    <meta property="linkedin:owner" content="nur-pratap-karki" />

    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Nur Pratap Karki" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nurpratapkarki.com.np/" />

    <!-- Alternate URLs for different languages/regions -->
    <link rel="alternate" hreflang="en" href="https://nurpratapkarki.com.np/" />
    <link rel="alternate" hreflang="ne" href="https://nurpratapkarki.com.np/ne/" />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://www.google-analytics.com" />

    <!-- DNS Prefetch for better performance -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//www.google-analytics.com" />
    <link rel="dns-prefetch" href="//www.googletagmanager.com" />

    <!-- Structured Data for Rich Snippets -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Nur Pratap Karki",
      "jobTitle": "Full Stack Developer",
      "description": "Expert Full Stack Developer specializing in React, Node.js, Python, and modern web technologies. Available for remote work globally.",
      "url": "https://nurpratapkarki.com.np",
      "image": "https://nurpratapkarki.com.np/profile-image.jpg",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Kathmandu",
        "addressCountry": "Nepal"
      },
      "nationality": "Nepali",
      "knowsAbout": [
        "Full Stack Development",
        "React.js",
        "Node.js",
        "Python",
        "JavaScript",
        "TypeScript",
        "Web Development",
        "API Development",
        "Database Design",
        "Cloud Computing",
        "DevOps",
        "Frontend Development",
        "Backend Development",
        "MERN Stack",
        "Software Engineering"
      ],
      "worksFor": {
        "@type": "Organization",
        "name": "Freelance"
      },
      "sameAs": [
        "https://github.com/nurpratapkarki",
        "https://linkedin.com/in/nurpratapkarki",
        "https://twitter.com/nurpratapkarki"
      ]
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Nur Pratap Karki Portfolio",
      "description": "Professional portfolio of Nur Pratap Karki, Full Stack Web Developer from Nepal",
      "url": "https://nurpratapkarki.com.np",
      "author": {
        "@type": "Person",
        "name": "Nur Pratap Karki"
      },
      "inLanguage": "en-US",
      "copyrightYear": "2024",
      "genre": "Portfolio",
      "keywords": "Full Stack Developer, Web Developer, React,  Python, Nepal, Kathmandu"
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      "name": "Nur Pratap Karki - Web Development Services",
      "description": "Professional web development services including full stack development, React applications, Node.js APIs, and Python solutions",
      "provider": {
        "@type": "Person",
        "name": "Nur Pratap Karki"
      },
      "areaServed": {
        "@type": "Country",
        "name": "Nepal"
      },
      "serviceType": [
        "Full Stack Web Development",
        "Frontend Development",
        "Backend Development",
        "API Development",
        "Database Design",
        "Web Application Development",
        "E-commerce Development",
        "Custom Software Solutions"
      ],
      "priceRange": "$$"
    }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
