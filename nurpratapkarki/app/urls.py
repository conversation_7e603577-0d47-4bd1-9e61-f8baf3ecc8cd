from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    ProfileViewSet, SkillCategoryViewSet, SkillViewSet,
    ProjectViewSet, BlogPostViewSet, BlogTagViewSet,
    TestimonialViewSet, ContactMessageViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'profile', ProfileViewSet, basename='profile')
router.register(r'skills/categories', SkillCategoryViewSet, basename='skillcategory')
router.register(r'skills', SkillViewSet, basename='skill')
router.register(r'projects', ProjectViewSet, basename='project')
router.register(r'blog/posts', BlogPostViewSet, basename='blogpost')
router.register(r'blog/tags', BlogTagViewSet, basename='blogtag')
router.register(r'testimonials', TestimonialViewSet, basename='testimonial')
router.register(r'contact', ContactMessageViewSet, basename='contact')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
