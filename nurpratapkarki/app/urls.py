from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ProfileViewSet, SkillCategoryViewSet, SkillViewSet,
    ProjectViewSet, BlogPostViewSet, BlogTagViewSet,
    TestimonialViewSet, ContactMessageViewSet, ExperienceViewSet, EducationViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'profile', ProfileViewSet, basename='profile')
router.register(r'skills/categories', SkillCategoryViewSet, basename='skillcategory')
router.register(r'skills', SkillViewSet, basename='skill')
router.register(r'projects', ProjectViewSet, basename='project')
router.register(r'blog/posts', BlogPostViewSet, basename='blogpost')
router.register(r'blog/tags', BlogTagViewSet, basename='blogtag')
router.register(r'testimonials', TestimonialViewSet, basename='testimonial')
router.register(r'experiences', ExperienceViewSet, basename='experience')
router.register(r'education', EducationViewSet, basename='education')
router.register(r'contact', ContactMessageViewSet, basename='contact')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
