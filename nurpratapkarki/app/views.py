from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from .models import (
    Profile, SkillCategory, Skill, Project, BlogPost,
    BlogTag, Testimonial, ContactMessage, Experience, Education
)
from .serializers import (
    ProfileSerializer, SkillCategorySerializer, SkillSerializer,
    ProjectSerializer, BlogPostSerializer, BlogTagSerializer,
    TestimonialSerializer, ContactMessageSerializer, ExperienceSerializer, EducationSerializer
)


class ProfileViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for user profile information
    """
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current profile (assuming single profile)"""
        profile = Profile.objects.first()
        if profile:
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        return Response({'detail': 'Profile not found'}, status=status.HTTP_404_NOT_FOUND)


class SkillCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for skill categories with their associated skills
    """
    queryset = SkillCategory.objects.prefetch_related('skills').all()
    serializer_class = SkillCategorySerializer


class SkillViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for individual skills
    """
    queryset = Skill.objects.select_related('category').all()
    serializer_class = SkillSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ['category', 'proficiency']
    search_fields = ['name']


class ProjectViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for portfolio projects
    """
    queryset = Project.objects.prefetch_related('technologies').all()
    serializer_class = ProjectSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'featured']
    search_fields = ['title', 'description', 'short_description']
    ordering_fields = ['created_at', 'order']
    lookup_field = 'slug'

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get only featured projects"""
        featured_projects = self.queryset.filter(featured=True)
        serializer = self.get_serializer(featured_projects, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get available project categories"""
        categories = Project.objects.values_list('category', flat=True).distinct()
        category_choices = dict(Project.CATEGORY_CHOICES)
        result = [{'value': cat, 'label': category_choices.get(cat, cat)} for cat in categories]
        return Response(result)


class BlogPostViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for blog posts
    """
    queryset = BlogPost.objects.prefetch_related('tags').filter(published=True)
    serializer_class = BlogPostSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'featured']
    search_fields = ['title', 'excerpt', 'content']
    ordering_fields = ['published_date', 'created_at']
    lookup_field = 'slug'

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get only featured blog posts"""
        featured_posts = self.queryset.filter(featured=True)
        serializer = self.get_serializer(featured_posts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get available blog categories"""
        categories = BlogPost.objects.filter(published=True).values_list('category', flat=True).distinct()
        return Response(list(categories))


class BlogTagViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for blog tags
    """
    queryset = BlogTag.objects.all()
    serializer_class = BlogTagSerializer


class TestimonialViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for client testimonials
    """
    queryset = Testimonial.objects.all()
    serializer_class = TestimonialSerializer
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['featured', 'rating']
    ordering_fields = ['created_at', 'order']

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get only featured testimonials"""
        featured_testimonials = self.queryset.filter(featured=True)
        serializer = self.get_serializer(featured_testimonials, many=True)
        return Response(serializer.data)


class ExperienceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for work experience entries
    """
    queryset = Experience.objects.prefetch_related('technologies').all()
    serializer_class = ExperienceSerializer


class EducationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for education entries
    """
    queryset = Education.objects.all()
    serializer_class = EducationSerializer


class ContactMessageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for contact messages - allows creation of new messages
    """
    queryset = ContactMessage.objects.all()
    serializer_class = ContactMessageSerializer
    http_method_names = ['post']  # Only allow POST requests

    def create(self, request, *args, **kwargs):
        """Create a new contact message"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {'message': 'Thank you for your message! I will get back to you soon.'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
