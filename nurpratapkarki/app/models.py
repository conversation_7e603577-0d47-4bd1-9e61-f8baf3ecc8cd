from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class Profile(models.Model):
    """User profile information for the portfolio"""
    name = models.CharField(max_length=100, default="<PERSON>ur Pratap Karki")
    title = models.CharField(max_length=200, default="Full Stack Developer")
    bio = models.TextField()
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    location = models.CharField(max_length=100, blank=True)
    github_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    resume_url = models.URLField(blank=True)
    profile_image = models.ImageField(upload_to='profile/', blank=True)
    years_experience = models.PositiveIntegerField(default=3)
    projects_completed = models.PositiveIntegerField(default=50)
    technologies_mastered = models.PositiveIntegerField(default=15)
    client_satisfaction = models.PositiveIntegerField(default=100)
    available_for_work = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Profile"
        verbose_name_plural = "Profile"

    def __str__(self):
        return self.name


class SkillCategory(models.Model):
    """Categories for organizing skills"""
    name = models.CharField(max_length=100)
    color_from = models.CharField(max_length=50, help_text="Tailwind color class (e.g., 'from-blue-500')")
    color_to = models.CharField(max_length=50, help_text="Tailwind color class (e.g., 'to-cyan-500')")
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'name']
        verbose_name = "Skill Category"
        verbose_name_plural = "Skill Categories"

    def __str__(self):
        return self.name

    @property
    def color_gradient(self):
        return f"{self.color_from} {self.color_to}"


class Skill(models.Model):
    """Individual skills/technologies"""
    name = models.CharField(max_length=100)
    category = models.ForeignKey(SkillCategory, on_delete=models.CASCADE, related_name='skills')
    proficiency = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        default=80,
        help_text="Proficiency level from 1-100"
    )
    years_experience = models.DecimalField(max_digits=3, decimal_places=1, default=1.0)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['category', 'order', 'name']
        unique_together = ['name', 'category']

    def __str__(self):
        return f"{self.name} ({self.category.name})"


class Project(models.Model):
    """Portfolio projects"""
    CATEGORY_CHOICES = [
        ('full_stack', 'Full Stack'),
        ('frontend', 'Frontend'),
        ('backend', 'Backend'),
        ('mobile', 'Mobile'),
        ('devops', 'DevOps'),
        ('other', 'Other'),
    ]

    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    description = models.TextField()
    short_description = models.CharField(max_length=300)
    image = models.ImageField(upload_to='projects/', blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    technologies = models.ManyToManyField(Skill, blank=True)
    github_url = models.URLField(blank=True)
    live_url = models.URLField(blank=True)
    featured = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-featured', 'order', '-created_at']

    def __str__(self):
        return self.title

    @property
    def technology_names(self):
        return [tech.name for tech in self.technologies.all()]


class BlogPost(models.Model):
    """Blog posts for the portfolio"""
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    excerpt = models.TextField(help_text="A short summary of the blog post")
    content = models.TextField()
    image = models.ImageField(upload_to='blog/', blank=True)
    category = models.CharField(max_length=100)
    read_time = models.CharField(max_length=20, default="5 min read")
    featured = models.BooleanField(default=False)
    published = models.BooleanField(default=True)
    published_date = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-published_date']

    def __str__(self):
        return self.title


class BlogTag(models.Model):
    """Tags for blog posts"""
    name = models.CharField(max_length=50, unique=True)
    blog_posts = models.ManyToManyField(BlogPost, related_name='tags')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class Testimonial(models.Model):
    """Client testimonials"""
    name = models.CharField(max_length=100)
    role = models.CharField(max_length=100)
    company = models.CharField(max_length=100)
    avatar = models.ImageField(upload_to='testimonials/', blank=True)
    content = models.TextField()
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        default=5
    )
    featured = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-featured', 'order', '-created_at']

    def __str__(self):
        return f"{self.name} - {self.company}"


class Experience(models.Model):
    """Work experience entries"""
    title = models.CharField(max_length=200)
    company = models.CharField(max_length=200)
    period = models.CharField(max_length=100)
    description = models.TextField()
    technologies = models.ManyToManyField(Skill, blank=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-created_at']

    def __str__(self):
        return f"{self.title} at {self.company}"

    @property
    def technology_names(self):
        return [tech.name for tech in self.technologies.all()]


class Education(models.Model):
    """Education entries"""
    degree = models.CharField(max_length=200)
    institution = models.CharField(max_length=200)
    period = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-created_at']

    def __str__(self):
        return f"{self.degree} - {self.institution}"


class ContactMessage(models.Model):
    """Messages from the contact form"""
    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.CharField(max_length=200)
    message = models.TextField()
    read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"
