# Generated by Django 5.2.4 on 2025-07-15 07:19

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BlogPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('excerpt', models.TextField(help_text='A short summary of the blog post')),
                ('content', models.TextField()),
                ('image', models.ImageField(blank=True, upload_to='blog/')),
                ('category', models.CharField(max_length=100)),
                ('read_time', models.CharField(default='5 min read', max_length=20)),
                ('featured', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('published', models.<PERSON>oleanField(default=True)),
                ('published_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-published_date'],
            },
        ),
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Nur Pratap Karki', max_length=100)),
                ('title', models.CharField(default='Full Stack Developer', max_length=200)),
                ('bio', models.TextField()),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('location', models.CharField(blank=True, max_length=100)),
                ('github_url', models.URLField(blank=True)),
                ('linkedin_url', models.URLField(blank=True)),
                ('twitter_url', models.URLField(blank=True)),
                ('resume_url', models.URLField(blank=True)),
                ('profile_image', models.ImageField(blank=True, upload_to='profile/')),
                ('years_experience', models.PositiveIntegerField(default=3)),
                ('projects_completed', models.PositiveIntegerField(default=50)),
                ('technologies_mastered', models.PositiveIntegerField(default=15)),
                ('client_satisfaction', models.PositiveIntegerField(default=100)),
                ('available_for_work', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Profile',
                'verbose_name_plural': 'Profile',
            },
        ),
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('proficiency', models.PositiveIntegerField(default=80, help_text='Proficiency level from 1-100', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('years_experience', models.DecimalField(decimal_places=1, default=1.0, max_digits=3)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['category', 'order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SkillCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('color_from', models.CharField(help_text="Tailwind color class (e.g., 'from-blue-500')", max_length=50)),
                ('color_to', models.CharField(help_text="Tailwind color class (e.g., 'to-cyan-500')", max_length=50)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Skill Category',
                'verbose_name_plural': 'Skill Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('role', models.CharField(max_length=100)),
                ('company', models.CharField(max_length=100)),
                ('avatar', models.ImageField(blank=True, upload_to='testimonials/')),
                ('content', models.TextField()),
                ('rating', models.PositiveIntegerField(default=5, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('featured', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-featured', 'order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BlogTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('blog_posts', models.ManyToManyField(related_name='tags', to='app.blogpost')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField()),
                ('short_description', models.CharField(max_length=300)),
                ('image', models.ImageField(blank=True, upload_to='projects/')),
                ('category', models.CharField(choices=[('full_stack', 'Full Stack'), ('frontend', 'Frontend'), ('backend', 'Backend'), ('mobile', 'Mobile'), ('devops', 'DevOps'), ('other', 'Other')], max_length=20)),
                ('github_url', models.URLField(blank=True)),
                ('live_url', models.URLField(blank=True)),
                ('featured', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('technologies', models.ManyToManyField(blank=True, to='app.skill')),
            ],
            options={
                'ordering': ['-featured', 'order', '-created_at'],
            },
        ),
        migrations.AddField(
            model_name='skill',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='app.skillcategory'),
        ),
        migrations.AlterUniqueTogether(
            name='skill',
            unique_together={('name', 'category')},
        ),
    ]
