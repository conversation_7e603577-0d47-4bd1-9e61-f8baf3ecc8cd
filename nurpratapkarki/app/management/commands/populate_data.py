from django.core.management.base import BaseCommand
from django.utils import timezone
from app.models import (
    Profile, SkillCategory, Skill, Project, BlogPost, 
    BlogTag, Testimonial, ContactMessage
)


class Command(BaseCommand):
    help = 'Populate the database with sample portfolio data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')

        # Create Profile
        profile, created = Profile.objects.get_or_create(
            defaults={
                'name': 'Nur Pratap Karki',
                'title': 'Full Stack Developer & Software Engineer',
                'bio': 'Passionate full-stack developer with 3+ years of experience building scalable web applications. I specialize in React, Node.js, Python, and cloud technologies. I love creating efficient, user-friendly solutions that solve real-world problems.',
                'email': '<EMAIL>',
                'phone': '+977-9800000000',
                'location': 'Kathmandu, Nepal',
                'github_url': 'https://github.com/nurpratapkarki',
                'linkedin_url': 'https://linkedin.com/in/nurpratapkarki',
                'twitter_url': 'https://twitter.com/nurpratapkarki',
                'years_experience': 3,
                'projects_completed': 50,
                'technologies_mastered': 15,
                'client_satisfaction': 100,
                'available_for_work': True,
            }
        )
        if created:
            self.stdout.write('✓ Profile created')

        # Create Skill Categories and Skills
        skill_data = [
            {
                'category': 'Frontend',
                'color_from': 'from-blue-500',
                'color_to': 'to-cyan-500',
                'skills': ['React', 'TypeScript', 'Next.js', 'Tailwind CSS', 'Framer Motion', 'Vite']
            },
            {
                'category': 'Backend',
                'color_from': 'from-green-500',
                'color_to': 'to-emerald-500',
                'skills': ['Node.js', 'Express', 'Python', 'PostgreSQL', 'MongoDB', 'Redis']
            },
            {
                'category': 'Cloud & DevOps',
                'color_from': 'from-purple-500',
                'color_to': 'to-pink-500',
                'skills': ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform', 'Nginx']
            },
            {
                'category': 'Tools & Others',
                'color_from': 'from-orange-500',
                'color_to': 'to-red-500',
                'skills': ['Git', 'VS Code', 'Figma', 'Jest', 'Cypress', 'Webpack']
            }
        ]

        for idx, cat_data in enumerate(skill_data):
            category, created = SkillCategory.objects.get_or_create(
                name=cat_data['category'],
                defaults={
                    'color_from': cat_data['color_from'],
                    'color_to': cat_data['color_to'],
                    'order': idx
                }
            )
            if created:
                self.stdout.write(f'✓ Skill category "{category.name}" created')

            for skill_idx, skill_name in enumerate(cat_data['skills']):
                skill, created = Skill.objects.get_or_create(
                    name=skill_name,
                    category=category,
                    defaults={
                        'proficiency': 85,
                        'years_experience': 2.5,
                        'order': skill_idx
                    }
                )
                if created:
                    self.stdout.write(f'  ✓ Skill "{skill.name}" created')

        # Create Blog Tags
        blog_tags = ['React', 'Next.js', 'TypeScript', 'Python', 'Django', 'AWS', 'Docker', 'DevOps', 'JavaScript', 'CSS']
        for tag_name in blog_tags:
            tag, created = BlogTag.objects.get_or_create(name=tag_name)
            if created:
                self.stdout.write(f'✓ Blog tag "{tag.name}" created')

        # Create Blog Posts
        blog_posts_data = [
            {
                'title': 'Building Scalable React Applications with Modern Architecture',
                'slug': 'building-scalable-react-apps',
                'excerpt': 'Learn how to structure React applications for scalability, maintainability, and performance. We\'ll explore component architecture, state management, and optimization techniques.',
                'category': 'React',
                'featured': True,
                'tags': ['React', 'JavaScript', 'TypeScript']
            },
            {
                'title': 'Next.js 14 App Router: Complete Guide to Modern Routing',
                'slug': 'nextjs-14-app-router',
                'excerpt': 'Dive deep into Next.js 14\'s new App Router, exploring nested layouts, loading states, error boundaries, and advanced routing patterns.',
                'category': 'Next.js',
                'featured': False,
                'tags': ['Next.js', 'React', 'TypeScript']
            },
            {
                'title': 'Advanced TypeScript Patterns for Better Code Quality',
                'slug': 'typescript-advanced-patterns',
                'excerpt': 'Master advanced TypeScript patterns including conditional types, template literals, and utility types to write more robust applications.',
                'category': 'TypeScript',
                'featured': False,
                'tags': ['TypeScript', 'JavaScript']
            }
        ]

        for post_data in blog_posts_data:
            post, created = BlogPost.objects.get_or_create(
                slug=post_data['slug'],
                defaults={
                    'title': post_data['title'],
                    'excerpt': post_data['excerpt'],
                    'content': f"This is the full content for {post_data['title']}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                    'category': post_data['category'],
                    'featured': post_data['featured'],
                    'published_date': timezone.now(),
                }
            )
            if created:
                # Add tags to the post
                for tag_name in post_data['tags']:
                    tag = BlogTag.objects.get(name=tag_name)
                    tag.blog_posts.add(post)
                self.stdout.write(f'✓ Blog post "{post.title}" created')

        # Create Projects
        projects_data = [
            {
                'title': 'E-Commerce Platform',
                'slug': 'ecommerce-platform',
                'description': 'A full-stack e-commerce platform built with React, Node.js, and PostgreSQL. Features include user authentication, payment processing, order management, and admin dashboard.',
                'short_description': 'Modern e-commerce platform with full payment integration',
                'category': 'full_stack',
                'technologies': ['React', 'Node.js', 'PostgreSQL'],
                'github_url': 'https://github.com/nurpratapkarki/ecommerce-platform',
                'live_url': 'https://ecommerce-demo.example.com',
                'featured': True,
            },
            {
                'title': 'Task Management App',
                'slug': 'task-management',
                'description': 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
                'short_description': 'Collaborative task management with real-time updates',
                'category': 'frontend',
                'technologies': ['React', 'TypeScript'],
                'github_url': 'https://github.com/nurpratapkarki/task-management',
                'live_url': 'https://tasks-demo.example.com',
                'featured': True,
            },
            {
                'title': 'API Gateway Service',
                'slug': 'api-gateway',
                'description': 'A robust API gateway built with Node.js for managing microservices communication, including rate limiting, authentication, and load balancing.',
                'short_description': 'High-performance API gateway for microservices',
                'category': 'backend',
                'technologies': ['Node.js', 'Express', 'Docker'],
                'github_url': 'https://github.com/nurpratapkarki/api-gateway',
                'live_url': 'https://api-gateway-demo.example.com',
                'featured': False,
            }
        ]

        for project_data in projects_data:
            project, created = Project.objects.get_or_create(
                slug=project_data['slug'],
                defaults={
                    'title': project_data['title'],
                    'description': project_data['description'],
                    'short_description': project_data['short_description'],
                    'category': project_data['category'],
                    'github_url': project_data['github_url'],
                    'live_url': project_data['live_url'],
                    'featured': project_data['featured'],
                }
            )
            if created:
                # Add technologies to the project
                for tech_name in project_data['technologies']:
                    try:
                        tech = Skill.objects.get(name=tech_name)
                        project.technologies.add(tech)
                    except Skill.DoesNotExist:
                        pass
                self.stdout.write(f'✓ Project "{project.title}" created')

        # Create Testimonials
        testimonials_data = [
            {
                'name': 'Sarah Chen',
                'role': 'Product Manager',
                'company': 'TechCorp Inc.',
                'content': 'Nur delivered exceptional work on our React application. His attention to detail and ability to translate complex requirements into elegant solutions is remarkable. The project was completed ahead of schedule and exceeded our expectations.',
                'rating': 5,
                'featured': True,
            },
            {
                'name': 'Michael Rodriguez',
                'role': 'CTO',
                'company': 'StartupXYZ',
                'content': 'Working with Nur was a game-changer for our startup. He built a scalable architecture that allowed us to grow from 100 to 10,000 users without any performance issues. His expertise in both frontend and backend development is impressive.',
                'rating': 5,
                'featured': True,
            },
            {
                'name': 'Emily Thompson',
                'role': 'Design Lead',
                'company': 'Creative Agency',
                'content': 'Nur has an incredible ability to bring designs to life with pixel-perfect precision. His collaboration skills and technical expertise made our design-to-development handoff seamless. Highly recommended!',
                'rating': 5,
                'featured': False,
            }
        ]

        for idx, testimonial_data in enumerate(testimonials_data):
            testimonial, created = Testimonial.objects.get_or_create(
                name=testimonial_data['name'],
                company=testimonial_data['company'],
                defaults={
                    'role': testimonial_data['role'],
                    'content': testimonial_data['content'],
                    'rating': testimonial_data['rating'],
                    'featured': testimonial_data['featured'],
                    'order': idx,
                }
            )
            if created:
                self.stdout.write(f'✓ Testimonial from "{testimonial.name}" created')

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
