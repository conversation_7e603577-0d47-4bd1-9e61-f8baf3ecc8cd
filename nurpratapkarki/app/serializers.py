from rest_framework import serializers
from .models import (
    Profile, SkillCategory, Skill, Project, BlogPost,
    BlogTag, Testimonial, ContactMessage, Experience, Education
)


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = ['id', 'name', 'proficiency', 'years_experience']


class SkillCategorySerializer(serializers.ModelSerializer):
    skills = SkillSerializer(many=True, read_only=True)
    technologies = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()

    class Meta:
        model = SkillCategory
        fields = ['id', 'name', 'color', 'technologies', 'skills', 'order']

    def get_technologies(self, obj):
        return [skill.name for skill in obj.skills.all()]

    def get_color(self, obj):
        return obj.color_gradient


class ProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = [
            'id', 'name', 'title', 'bio', 'email', 'phone', 'location',
            'github_url', 'linkedin_url', 'twitter_url', 'resume_url',
            'profile_image', 'years_experience', 'projects_completed',
            'technologies_mastered', 'client_satisfaction', 'available_for_work'
        ]


class ProjectSerializer(serializers.ModelSerializer):
    technologies = serializers.SerializerMethodField()
    category_display = serializers.CharField(source='get_category_display', read_only=True)

    class Meta:
        model = Project
        fields = [
            'id', 'title', 'slug', 'description', 'short_description',
            'image', 'category', 'category_display', 'technologies',
            'github_url', 'live_url', 'featured', 'created_at'
        ]

    def get_technologies(self, obj):
        return obj.technology_names


class BlogTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogTag
        fields = ['id', 'name']


class BlogPostSerializer(serializers.ModelSerializer):
    tags = BlogTagSerializer(many=True, read_only=True)
    date = serializers.DateTimeField(source='published_date', read_only=True)
    read_time = serializers.CharField(read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'slug', 'excerpt', 'content', 'image',
            'category', 'tags', 'read_time', 'featured', 'date', 'published_date'
        ]


class TestimonialSerializer(serializers.ModelSerializer):
    class Meta:
        model = Testimonial
        fields = [
            'id', 'name', 'role', 'company', 'avatar', 'content',
            'rating', 'featured', 'created_at'
        ]


class ExperienceSerializer(serializers.ModelSerializer):
    technologies = serializers.SerializerMethodField()

    class Meta:
        model = Experience
        fields = ['id', 'title', 'company', 'period', 'description', 'technologies', 'order']

    def get_technologies(self, obj):
        return obj.technology_names


class EducationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Education
        fields = ['id', 'degree', 'institution', 'period', 'description', 'order']


class ContactMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactMessage
        fields = ['id', 'name', 'email', 'subject', 'message', 'created_at']
        read_only_fields = ['id', 'created_at']
