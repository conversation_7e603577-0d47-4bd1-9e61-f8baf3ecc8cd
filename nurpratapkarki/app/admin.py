from django.contrib import admin
from .models import (
    Profile, SkillCategory, Skill, Project, BlogPost,
    BlogTag, Testimonial, ContactMessage, Experience, Education
)


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ['name', 'title', 'email', 'available_for_work', 'updated_at']
    list_filter = ['available_for_work']
    search_fields = ['name', 'title', 'email']


@admin.register(SkillCategory)
class SkillCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'order', 'color_gradient']
    list_editable = ['order']
    ordering = ['order']


@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'proficiency', 'years_experience', 'order']
    list_filter = ['category', 'proficiency']
    list_editable = ['order', 'proficiency']
    search_fields = ['name']
    ordering = ['category', 'order']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'featured', 'order', 'created_at']
    list_filter = ['category', 'featured', 'created_at']
    list_editable = ['featured', 'order']
    search_fields = ['title', 'description']
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ['technologies']
    ordering = ['-featured', 'order']


@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'featured', 'published', 'published_date']
    list_filter = ['category', 'featured', 'published', 'published_date']
    list_editable = ['featured', 'published']
    search_fields = ['title', 'excerpt', 'content']
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'published_date'


@admin.register(BlogTag)
class BlogTagAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['name', 'company', 'role', 'rating', 'featured', 'order']
    list_filter = ['rating', 'featured', 'company']
    list_editable = ['featured', 'order', 'rating']
    search_fields = ['name', 'company', 'content']
    ordering = ['-featured', 'order']


@admin.register(Experience)
class ExperienceAdmin(admin.ModelAdmin):
    list_display = ['title', 'company', 'period', 'order', 'created_at']
    list_editable = ['order']
    search_fields = ['title', 'company', 'description']
    filter_horizontal = ['technologies']
    ordering = ['order', '-created_at']

    fieldsets = (
        (None, {
            'fields': ('title', 'company', 'period', 'description', 'order')
        }),
        ('Technologies', {
            'fields': ('technologies',),
            'classes': ('collapse',)
        }),
    )


@admin.register(Education)
class EducationAdmin(admin.ModelAdmin):
    list_display = ['degree', 'institution', 'period', 'order', 'created_at']
    list_editable = ['order']
    search_fields = ['degree', 'institution', 'description']
    ordering = ['order', '-created_at']

    fieldsets = (
        (None, {
            'fields': ('degree', 'institution', 'period', 'description', 'order')
        }),
    )


@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'subject', 'read', 'created_at']
    list_filter = ['read', 'created_at']
    list_editable = ['read']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['created_at']
    ordering = ['-created_at']
